
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface LoadingScreenProps {
  onLoadingComplete: () => void;
}

const LoadingScreen = ({ onLoadingComplete }: LoadingScreenProps) => {
  const [progress, setProgress] = useState(0);
  const [loadingStage, setLoadingStage] = useState('Initializing...');
  const [isComplete, setIsComplete] = useState(false);

  const loadingStages = [
    'Initializing ScrollFit...',
    'Loading Player Sprites...',
    'Loading Game Field...',
    'Loading Collectibles...',
    'Loading Sound Effects...',
    'Preparing Game Pages...',
    'Caching Assets...',
    'Ready to Play!'
  ];

  useEffect(() => {
    const loadGame = async () => {
      for (let i = 0; i < loadingStages.length; i++) {
        setLoadingStage(loadingStages[i]);

        // Fast loading times for online game (no rendering delays)
        const loadTime = i === loadingStages.length - 1 ? 500 : 200 + Math.random() * 200;

        await new Promise(resolve => setTimeout(resolve, loadTime));

        setProgress((i + 1) / loadingStages.length * 100);
      }

      setIsComplete(true);
      setTimeout(onLoadingComplete, 800);
    };

    loadGame();
  }, [onLoadingComplete]);

  return (
    <div className="fixed inset-0 z-50 flex flex-col items-center justify-center px-4 sm:px-6 lg:px-8" style={{
      background: 'linear-gradient(135deg, #0A0B1A 0%, #161B33 50%, #0D1117 100%)'
    }}>
      {/* Animated Background Particles */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 sm:w-2 sm:h-2 bg-white/10 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: [0.1, 0.6, 0.1],
              scale: [1, 2, 1],
              rotate: [0, 360]
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Main Content Container */}
      <div className="flex flex-col items-center justify-center w-full max-w-md mx-auto">
        {/* ScrollFit Logo Image */}
        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 1, ease: "easeOut" }}
          className="text-center mb-8 sm:mb-12"
        >
          {/* Main ScrollFit Image */}
          <motion.div
            animate={{
              scale: [1, 1.05, 1],
              filter: [
                'drop-shadow(0 0 20px rgba(57, 255, 20, 0.3))',
                'drop-shadow(0 0 40px rgba(57, 255, 20, 0.5))',
                'drop-shadow(0 0 20px rgba(57, 255, 20, 0.3))'
              ]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="mb-4 sm:mb-6"
          >
            <img
              src="/scrollfit.png"
              alt="ScrollFit Logo"
              className="w-48 h-48 sm:w-64 sm:h-64 md:w-80 md:h-80 lg:w-96 lg:h-96 object-contain mx-auto"
              style={{
                filter: 'drop-shadow(0 0 30px rgba(57, 255, 20, 0.4))'
              }}
            />
          </motion.div>

          {/* Game Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.8 }}
            className="text-sm sm:text-lg md:text-xl text-[#B8BCC8] font-medium px-4"
          >
            Mental Fitness Gridiron Glory
          </motion.p>
        </motion.div>

        {/* Progress Section - Positioned at bottom */}
        <div className="w-full max-w-xs sm:max-w-sm md:max-w-md">
          {/* Progress Bar */}
          <div
            className="w-full h-3 sm:h-4 md:h-5 rounded-full mb-4 sm:mb-6 overflow-hidden border border-white/20"
            style={{
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(20px)'
            }}
          >
            <motion.div
              className="h-full rounded-full relative overflow-hidden"
              style={{
                background: 'linear-gradient(90deg, #39FF14, #00FF88, #39FF14)',
                width: `${progress}%`
              }}
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.5, ease: "easeOut" }}
            >
              <motion.div
                className="absolute inset-0"
                animate={{
                  background: [
                    'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)',
                    'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)'
                  ],
                  x: ['-100%', '100%']
                }}
                transition={{ duration: 1.5, repeat: Infinity }}
              />
            </motion.div>
          </div>

          {/* Progress Text */}
          <div className="text-center">
            <motion.div
              key={loadingStage}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="text-white font-bold text-sm sm:text-base md:text-lg mb-2 px-2"
            >
              {loadingStage}
            </motion.div>

            <motion.div
              className="text-[#39FF14] font-black text-2xl sm:text-3xl md:text-4xl"
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 0.5 }}
            >
              {Math.round(progress)}%
            </motion.div>
          </div>
        </div>
      </div>

      {/* Ready Animation */}
      <AnimatePresence>
        {isComplete && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 1.2, opacity: 0 }}
            className="absolute inset-0 flex items-center justify-center px-4"
            style={{
              background: 'rgba(57, 255, 20, 0.1)',
              backdropFilter: 'blur(10px)'
            }}
          >
            <motion.div
              animate={{
                scale: [1, 1.2, 1],
                rotate: [0, 360],
              }}
              transition={{ duration: 1 }}
              className="text-4xl sm:text-6xl md:text-8xl"
            >
              ✅
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default LoadingScreen;
