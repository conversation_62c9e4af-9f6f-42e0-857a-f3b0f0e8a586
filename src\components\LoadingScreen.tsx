
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface LoadingScreenProps {
  onLoadingComplete: () => void;
}

const LoadingScreen = ({ onLoadingComplete }: LoadingScreenProps) => {
  const [progress, setProgress] = useState(0);
  const [loadingStage, setLoadingStage] = useState('Initializing...');
  const [isComplete, setIsComplete] = useState(false);

  const loadingStages = [
    'Initializing ScrollFit...',
    'Loading Player Sprites...',
    'Loading Game Field...',
    'Loading Collectibles...',
    'Loading Sound Effects...',
    'Preparing Game Pages...',
    'Caching Assets...',
    'Ready to Play!'
  ];

  useEffect(() => {
    const loadGame = async () => {
      for (let i = 0; i < loadingStages.length; i++) {
        setLoadingStage(loadingStages[i]);
        
        // Simulate realistic loading times
        const loadTime = i === loadingStages.length - 1 ? 800 : 400 + Math.random() * 300;
        
        await new Promise(resolve => setTimeout(resolve, loadTime));
        
        setProgress((i + 1) / loadingStages.length * 100);
      }
      
      setIsComplete(true);
      setTimeout(onLoadingComplete, 1000);
    };

    loadGame();
  }, [onLoadingComplete]);

  return (
    <div className="fixed inset-0 z-50 flex flex-col items-center justify-center" style={{
      background: 'linear-gradient(135deg, #0A0B1A 0%, #161B33 50%, #0D1117 100%)'
    }}>
      {/* Animated Background Particles */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(30)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-white/10 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: [0.1, 0.6, 0.1],
              scale: [1, 2, 1],
              rotate: [0, 360]
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Main Logo Animation */}
      <motion.div
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 1, ease: "easeOut" }}
        className="text-center mb-12"
      >
        <motion.div
          animate={{
            textShadow: [
              '0 0 20px #39FF14',
              '0 0 40px #39FF14, 0 0 60px #39FF14',
              '0 0 20px #39FF14'
            ]
          }}
          transition={{ duration: 2, repeat: Infinity }}
          className="text-6xl font-black text-white mb-4"
        >
          SCROLLFIT
        </motion.div>
        
        {/* Spinning Football */}
        <motion.div
          animate={{ 
            rotate: 360,
            scale: [1, 1.2, 1]
          }}
          transition={{ 
            rotate: { duration: 2, repeat: Infinity, ease: "linear" },
            scale: { duration: 1.5, repeat: Infinity }
          }}
          className="text-8xl mb-6"
          style={{
            filter: 'drop-shadow(0 0 30px #FFD700)'
          }}
        >
          🏈
        </motion.div>
        
        <p className="text-xl text-[#B8BCC8] font-medium">
          Mental Fitness Gridiron Glory
        </p>
      </motion.div>

      {/* Progress Section */}
      <div className="w-80 max-w-sm">
        {/* Progress Bar */}
        <div 
          className="w-full h-4 rounded-full mb-6 overflow-hidden border border-white/20"
          style={{
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(20px)'
          }}
        >
          <motion.div
            className="h-full rounded-full relative overflow-hidden"
            style={{
              background: 'linear-gradient(90deg, #39FF14, #00FF88, #39FF14)',
              width: `${progress}%`
            }}
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          >
            <motion.div
              className="absolute inset-0"
              animate={{
                background: [
                  'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)',
                  'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)'
                ],
                x: ['-100%', '100%']
              }}
              transition={{ duration: 1.5, repeat: Infinity }}
            />
          </motion.div>
        </div>

        {/* Progress Text */}
        <div className="text-center">
          <motion.div
            key={loadingStage}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="text-white font-bold text-lg mb-2"
          >
            {loadingStage}
          </motion.div>
          
          <motion.div
            className="text-[#39FF14] font-black text-3xl"
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 0.5 }}
          >
            {Math.round(progress)}%
          </motion.div>
        </div>
      </div>

      {/* Ready Animation */}
      <AnimatePresence>
        {isComplete && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 1.2, opacity: 0 }}
            className="absolute inset-0 flex items-center justify-center"
            style={{
              background: 'rgba(57, 255, 20, 0.1)',
              backdropFilter: 'blur(10px)'
            }}
          >
            <motion.div
              animate={{
                scale: [1, 1.2, 1],
                rotate: [0, 360],
              }}
              transition={{ duration: 1 }}
              className="text-8xl"
            >
              ✅
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default LoadingScreen;
