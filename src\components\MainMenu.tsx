
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import CharacterSelection from './CharacterSelection';
import StatsPage from './StatsPage';
import SettingsPage from './SettingsPage';
import TrophiesPage from './TrophiesPage';
import PlayerAccount from './PlayerAccount';
import { User, Trophy, BarChart3, Settings, Play, Crown, UserCog } from 'lucide-react';

interface MainMenuProps {
  onStartGame: () => void;
  onCustomize: () => void;
  onAccount?: () => void;
  highScore: number;
  selectedSprite: number;
  onSpriteSelect: (spriteId: number) => void;
}

type ActivePage = 'main' | 'characters' | 'stats' | 'settings' | 'trophies' | 'account';

const MainMenu = ({ onStartGame, onCustomize, onAccount, highScore, selectedSprite, onSpriteSelect }: MainMenuProps) => {
  const [activePage, setActivePage] = useState<ActivePage>('main');
  const [coins] = useState(2850);
  const [level] = useState(15);
  const [streak] = useState(7);

  const renderPage = () => {
    switch (activePage) {
      case 'characters':
        return (
          <CharacterSelection
            selectedSprite={selectedSprite}
            onSpriteSelect={onSpriteSelect}
            onBack={() => setActivePage('main')}
          />
        );
      case 'stats':
        return <StatsPage onBack={() => setActivePage('main')} />;
      case 'settings':
        return <SettingsPage onBack={() => setActivePage('main')} />;
      case 'trophies':
        return <TrophiesPage onBack={() => setActivePage('main')} />;
      case 'account':
        return <PlayerAccount onBack={() => setActivePage('main')} />;
      default:
        return null;
    }
  };

  if (activePage !== 'main') {
    return renderPage();
  }

  return (
    <div className="h-screen w-full relative overflow-hidden flex flex-col" style={{
      background: 'linear-gradient(135deg, #0A0B1A 0%, #161B33 50%, #0D1117 100%)'
    }}>
      {/* Gaming Background Effects */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(40)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full"
            style={{
              width: Math.random() * 3 + 1,
              height: Math.random() * 3 + 1,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              background: i % 3 === 0 ? '#39FF14' : i % 3 === 1 ? '#00D4FF' : '#FF6B35'
            }}
            animate={{
              opacity: [0.1, 0.8, 0.1],
              scale: [1, 2, 1],
              y: [0, -20, 0]
            }}
            transition={{
              duration: 2 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 flex flex-col h-full">
        {/* Compact Header */}
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          className="pt-6 px-4 flex-shrink-0"
        >
          <div className="flex justify-between items-center mb-4">
            {/* Gaming Logo */}
            <motion.div
              animate={{
                textShadow: [
                  '0 0 20px #39FF14',
                  '0 0 40px #39FF14, 0 0 60px #00D4FF',
                  '0 0 20px #39FF14'
                ]
              }}
              transition={{ duration: 3, repeat: Infinity }}
              className="relative"
            >
              <h1 className="text-3xl font-black text-white relative">
                SCROLLFIT
                <motion.div
                  className="absolute -top-1 -right-6 text-xl"
                  animate={{ rotate: [0, 15, -15, 0] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  🏈
                </motion.div>
              </h1>
            </motion.div>

            {/* Compact Profile Section */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="flex items-center space-x-2"
            >
              {/* Account Button */}
              <motion.button
                whileHover={{ scale: 1.1, boxShadow: '0 0 25px rgba(57, 255, 20, 0.5)' }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setActivePage('account')}
                className="w-10 h-10 rounded-full flex items-center justify-center border-2 border-[#39FF14] relative overflow-hidden"
                style={{
                  background: 'linear-gradient(135deg, rgba(57, 255, 20, 0.2), rgba(0, 212, 255, 0.2))',
                  backdropFilter: 'blur(20px)'
                }}
              >
                <UserCog className="w-5 h-5 text-[#39FF14]" />
              </motion.button>

              {/* Compact Coins Display */}
              <motion.div 
                whileHover={{ scale: 1.05 }}
                className="flex items-center space-x-1 px-3 py-2 rounded-full border border-[#FFD700]/30 relative"
                style={{
                  background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 107, 53, 0.1))',
                  backdropFilter: 'blur(20px)'
                }}
              >
                <span className="text-xl">🪙</span>
                <span className="text-[#FFD700] font-black text-sm">{coins.toLocaleString()}</span>
              </motion.div>

              {/* Compact Profile Avatar */}
              <motion.div 
                whileHover={{ scale: 1.1 }}
                className="relative w-12 h-12 rounded-full border-2 border-[#00D4FF] flex items-center justify-center overflow-hidden"
                style={{
                  background: 'linear-gradient(45deg, #00D4FF, #39FF14)',
                  boxShadow: '0 0 20px rgba(0, 212, 255, 0.4)'
                }}
              >
                <User className="w-6 h-6 text-black" />
                <motion.div 
                  className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-r from-[#FF6B35] to-[#F7931E] rounded-full flex items-center justify-center border-2 border-[#0A0B1A]"
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <span className="text-xs font-black text-white">{level}</span>
                </motion.div>
              </motion.div>
            </motion.div>
          </div>

          {/* Compact Welcome Section */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3 }}
            className="text-center mb-4 p-3 rounded-xl border border-[#39FF14]/20"
            style={{
              background: 'linear-gradient(135deg, rgba(57, 255, 20, 0.05), rgba(0, 212, 255, 0.05))',
              backdropFilter: 'blur(15px)'
            }}
          >
            <h2 className="text-xl font-black text-white mb-2">
              READY TO DOMINATE? 🔥
            </h2>
            <div className="flex justify-center items-center space-x-6">
              <div className="text-center">
                <div className="text-[#39FF14] font-black text-lg">{streak} Days</div>
                <div className="text-[#B8BCC8] text-xs font-bold">STREAK 🚀</div>
              </div>
              <div className="text-center">
                <div className="text-[#00D4FF] font-black text-lg">Level {level}</div>
                <div className="text-[#B8BCC8] text-xs font-bold">CHAMPION ⚡</div>
              </div>
            </div>
          </motion.div>
        </motion.div>

        {/* Main Content - No Scrolling */}
        <div className="flex-1 px-4 pb-4 flex flex-col justify-between">
          {/* Epic Play Button */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            whileHover={{ scale: 1.02, y: -5 }}
            whileTap={{ scale: 0.98 }}
            onClick={onStartGame}
            className="relative cursor-pointer group mb-4"
          >
            <div 
              className="p-6 rounded-2xl border-2 border-[#39FF14] relative overflow-hidden"
              style={{
                background: 'linear-gradient(145deg, rgba(57, 255, 20, 0.15), rgba(0, 255, 136, 0.15))',
                backdropFilter: 'blur(25px)',
                boxShadow: '0 20px 60px rgba(57, 255, 20, 0.3)'
              }}
            >
              <div className="relative z-10 flex items-center justify-between">
                <div>
                  <h3 className="text-2xl font-black text-white mb-2">
                    ⚡ ENTER THE ARENA ⚡
                  </h3>
                  <p className="text-[#39FF14] text-sm font-bold">
                    BOOST YOUR MENTAL POWER NOW!
                  </p>
                </div>
                <motion.div
                  animate={{ 
                    x: [0, 10, 0],
                    rotate: [0, 5, 0]
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="w-16 h-16 rounded-full flex items-center justify-center"
                  style={{
                    background: 'linear-gradient(45deg, #39FF14, #00FF88)',
                    boxShadow: '0 0 30px rgba(57, 255, 20, 0.7)'
                  }}
                >
                  <Play className="w-8 h-8 text-black ml-1" />
                </motion.div>
              </div>
            </div>
          </motion.div>

          {/* Compact Gaming Cards Grid */}
          <div className="grid grid-cols-2 gap-3 mb-4">
            {/* Players Card */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5 }}
              whileHover={{ scale: 1.05, y: -3 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActivePage('characters')}
              className="cursor-pointer group"
            >
              <div 
                className="p-4 rounded-xl border-2 border-[#00D4FF]/30 h-24 relative overflow-hidden"
                style={{
                  background: 'linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(59, 130, 246, 0.1))',
                  backdropFilter: 'blur(20px)',
                  boxShadow: '0 10px 30px rgba(0, 212, 255, 0.2)'
                }}
              >
                <User className="w-8 h-8 text-[#00D4FF] mb-2" />
                <h4 className="text-white font-black text-sm mb-1">WARRIORS</h4>
                <p className="text-[#00D4FF] text-xs font-bold">Choose Fighter</p>
              </div>
            </motion.div>

            {/* Trophies Card */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.6 }}
              whileHover={{ scale: 1.05, y: -3 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActivePage('trophies')}
              className="cursor-pointer group"
            >
              <div 
                className="p-4 rounded-xl border-2 border-[#FFD700]/30 h-24 relative overflow-hidden"
                style={{
                  background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 107, 53, 0.1))',
                  backdropFilter: 'blur(20px)',
                  boxShadow: '0 10px 30px rgba(255, 215, 0, 0.2)'
                }}
              >
                <Trophy className="w-8 h-8 text-[#FFD700] mb-2" />
                <h4 className="text-white font-black text-sm mb-1">LEGENDS</h4>
                <p className="text-[#FFD700] text-xs font-bold">12/50 Earned</p>
              </div>
            </motion.div>

            {/* Stats Card */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.7 }}
              whileHover={{ scale: 1.05, y: -3 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActivePage('stats')}
              className="cursor-pointer group"
            >
              <div 
                className="p-4 rounded-xl border-2 border-[#39FF14]/30 h-24 relative overflow-hidden"
                style={{
                  background: 'linear-gradient(135deg, rgba(57, 255, 20, 0.1), rgba(0, 255, 127, 0.1))',
                  backdropFilter: 'blur(20px)',
                  boxShadow: '0 10px 30px rgba(57, 255, 20, 0.2)'
                }}
              >
                <BarChart3 className="w-8 h-8 text-[#39FF14] mb-2" />
                <h4 className="text-white font-black text-sm mb-1">POWER</h4>
                <p className="text-[#39FF14] text-xs font-bold">Performance</p>
              </div>
            </motion.div>

            {/* Settings Card */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.8 }}
              whileHover={{ scale: 1.05, y: -3 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActivePage('settings')}
              className="cursor-pointer group"
            >
              <div 
                className="p-4 rounded-xl border-2 border-[#9CA3AF]/30 h-24 relative overflow-hidden"
                style={{
                  background: 'linear-gradient(135deg, rgba(156, 163, 175, 0.1), rgba(107, 114, 128, 0.1))',
                  backdropFilter: 'blur(20px)',
                  boxShadow: '0 10px 30px rgba(156, 163, 175, 0.2)'
                }}
              >
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                >
                  <Settings className="w-8 h-8 text-[#9CA3AF] mb-2" />
                </motion.div>
                <h4 className="text-white font-black text-sm mb-1">CONTROL</h4>
                <p className="text-[#9CA3AF] text-xs font-bold">Settings</p>
              </div>
            </motion.div>
          </div>

          {/* Compact High Score Display */}
          {highScore > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.9 }}
              className="text-center"
            >
              <motion.div 
                whileHover={{ scale: 1.05 }}
                className="inline-flex items-center space-x-3 px-6 py-3 rounded-full border-2 border-[#FFD700]/30 relative"
                style={{
                  background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 107, 53, 0.1))',
                  backdropFilter: 'blur(25px)',
                  boxShadow: '0 15px 50px rgba(255, 215, 0, 0.3)'
                }}
              >
                <motion.div
                  animate={{ rotate: [0, 15, -15, 0] }}
                  transition={{ duration: 3, repeat: Infinity }}
                >
                  <Crown className="w-6 h-6 text-[#FFD700]" />
                </motion.div>
                <div>
                  <div className="text-[#FFD700] text-xs font-bold uppercase tracking-wider">
                    CHAMPION RECORD
                  </div>
                  <div 
                    className="text-xl font-black text-white"
                    style={{ textShadow: '0 0 25px #FFD700' }}
                  >
                    {highScore.toLocaleString()}
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MainMenu;
