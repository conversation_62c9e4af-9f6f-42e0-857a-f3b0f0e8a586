/**
 * Asset Preloader for ScrollFit Game
 * Ensures all critical assets are loaded before game starts
 * Optimized for online mobile gaming with zero delays
 */

interface PreloadedAsset {
  url: string;
  type: 'image' | 'audio' | 'font';
  loaded: boolean;
  element?: HTMLImageElement | HTMLAudioElement;
}

class AssetPreloader {
  private assets: Map<string, PreloadedAsset> = new Map();
  private loadingPromises: Promise<void>[] = [];

  /**
   * Add an asset to the preload queue
   */
  addAsset(url: string, type: 'image' | 'audio' | 'font' = 'image'): void {
    if (this.assets.has(url)) return;

    const asset: PreloadedAsset = {
      url,
      type,
      loaded: false
    };

    this.assets.set(url, asset);
  }

  /**
   * Preload a single image
   */
  private preloadImage(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        const asset = this.assets.get(url);
        if (asset) {
          asset.loaded = true;
          asset.element = img;
        }
        resolve();
      };

      img.onerror = () => {
        console.warn(`Failed to load image: ${url}`);
        reject(new Error(`Failed to load image: ${url}`));
      };

      // Set crossOrigin for external images
      if (url.startsWith('http')) {
        img.crossOrigin = 'anonymous';
      }

      img.src = url;
    });
  }

  /**
   * Preload a single audio file
   */
  private preloadAudio(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const audio = new Audio();
      
      audio.oncanplaythrough = () => {
        const asset = this.assets.get(url);
        if (asset) {
          asset.loaded = true;
          asset.element = audio;
        }
        resolve();
      };

      audio.onerror = () => {
        console.warn(`Failed to load audio: ${url}`);
        reject(new Error(`Failed to load audio: ${url}`));
      };

      audio.src = url;
      audio.load();
    });
  }

  /**
   * Preload a font
   */
  private preloadFont(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'font';
      link.type = 'font/woff2';
      link.crossOrigin = 'anonymous';
      
      link.onload = () => {
        const asset = this.assets.get(url);
        if (asset) {
          asset.loaded = true;
        }
        resolve();
      };

      link.onerror = () => {
        console.warn(`Failed to load font: ${url}`);
        reject(new Error(`Failed to load font: ${url}`));
      };

      link.href = url;
      document.head.appendChild(link);
    });
  }

  /**
   * Start preloading all assets
   */
  async preloadAll(): Promise<void> {
    this.loadingPromises = [];

    for (const [url, asset] of this.assets) {
      if (asset.loaded) continue;

      let promise: Promise<void>;

      switch (asset.type) {
        case 'image':
          promise = this.preloadImage(url);
          break;
        case 'audio':
          promise = this.preloadAudio(url);
          break;
        case 'font':
          promise = this.preloadFont(url);
          break;
        default:
          promise = this.preloadImage(url);
      }

      // Don't let individual asset failures stop the entire loading process
      const safePromise = promise.catch(error => {
        console.warn(`Asset loading failed: ${url}`, error);
      });

      this.loadingPromises.push(safePromise);
    }

    await Promise.all(this.loadingPromises);
  }

  /**
   * Get loading progress (0-100)
   */
  getProgress(): number {
    const total = this.assets.size;
    if (total === 0) return 100;

    const loaded = Array.from(this.assets.values()).filter(asset => asset.loaded).length;
    return Math.round((loaded / total) * 100);
  }

  /**
   * Check if all assets are loaded
   */
  isComplete(): boolean {
    return Array.from(this.assets.values()).every(asset => asset.loaded);
  }

  /**
   * Get a preloaded asset element
   */
  getAsset(url: string): HTMLImageElement | HTMLAudioElement | null {
    const asset = this.assets.get(url);
    return asset?.element || null;
  }

  /**
   * Clear all assets
   */
  clear(): void {
    this.assets.clear();
    this.loadingPromises = [];
  }
}

// Create singleton instance
export const assetPreloader = new AssetPreloader();

// Game-specific asset preloader
export const preloadGameAssets = async (): Promise<void> => {
  // Critical game assets
  const criticalAssets = [
    '/scrollfit.png',
    // Add other critical assets here as needed
  ];

  // Add all critical assets to preloader
  criticalAssets.forEach(url => {
    assetPreloader.addAsset(url, 'image');
  });

  // Preload all assets
  await assetPreloader.preloadAll();
};

export default assetPreloader;
